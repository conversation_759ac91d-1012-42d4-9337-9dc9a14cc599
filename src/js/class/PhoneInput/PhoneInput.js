import { AsYouType, parsePhoneNumberFromString } from 'libphonenumber-js/min';
import { Flag } from './Flag';
import { Input } from './Input';
import { Placeholder } from './Placeholder';
import { Select } from './Select';
import { Prefix } from './Prefix';

export class PhoneInput {
	constructor(element, supportedCountries, options = {}) {
		if (element) {
			const defaultOptions = {
				defaultCountry: 'CZ',
				className: 'js-phone-input',
				selectCountry: true,
				showPrefix: false,
				flagPath: '/static/img/flags/',
				flagExt: '.svg',
				numberFormat: 'E.164',
				allowedKeys: ['Backspace', 'Delete', '+'],
				metaKeys: ['Home', 'End', 'ArrowLeft', 'ArrowRight', 'Escape', 'Meta', 'Shift', 'Control', 'Tab'],
				supportedCountries,
			};

			this.baseElement = element;
			this.options = Object.assign({}, defaultOptions, options);
			this.countryCode = this.getBaseCountry();
			this.formatter = new AsYouType(this.countryCode);
			this.callingCodes = this.formatter.metadata.metadata.country_calling_codes;
			this.wrapper = this.addMarkup();
			this.hideBaseElement();
			this.setupEvents();
			this.setDefaultValue();
		}
	}

	hideBaseElement() {
		this.baseElement.classList.add(`${this.options.className}__base`);
		this.baseElement.setAttribute('tabIndex', '-1');
	}

	addMarkup() {
		const wrapper = document.createElement('span');
		this.parts = {};
		wrapper.classList.add(`${this.options.className}__wrapper`);

		this.parts.input = new Input(this.options, this.countryCode);
		wrapper.appendChild(this.parts.input.get());
		this.parts.placeholder = new Placeholder(this.options, this.countryCode);
		wrapper.appendChild(this.parts.placeholder.get());

		if (this.options.selectCountry && this.options.supportedCountries) {
			wrapper.classList.add(`${this.options.className}--has-country`);

			this.parts.flag = new Flag(this.options, this.countryCode);
			wrapper.appendChild(this.parts.flag.get());

			this.parts.select = new Select(this.options, this.countryCode);
			wrapper.appendChild(this.parts.select.get());

			if (this.options.showPrefix) {
				this.parts.prefix = new Prefix(this.options, this.countryCode);
				this.parts.flag.get().appendChild(this.parts.prefix.get());
			}
		}

		this.baseElement.parentNode.insertBefore(wrapper, this.baseElement);
		wrapper.appendChild(this.baseElement);

		return wrapper;
	}

	setupEvents() {
		const { input, select } = this.parts;
		if (select) {
			select.get().addEventListener('change', (event) => this.handleSelectChange(event));
		}

		if (input) {
			input.get().addEventListener('paste', (event) => this.handleInputPaste(event));
			input.get().addEventListener('keydown', (event) => this.handleInputKeyDown(event));
			input.get().addEventListener('keyup', (event) => this.handleInputKeyUp(event));

			if (this.baseElement.labels.length) {
				this.baseElement.labels.forEach((label) => label.addEventListener('click', (event) => this.handleLabelClick(event)));
			}
		}
	}

	getBaseCountry() {
		let baseCountry = this.baseElement.getAttribute('data-baseCountry');

		if (!baseCountry) {
			baseCountry = this.options.defaultCountry;
		}

		return baseCountry;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
		this.formatter = new AsYouType(countryCode);

		Object.keys(this.parts).forEach((key) => {
			const part = this.parts[key];
			if (part && typeof part.setCountry === 'function') part.setCountry(countryCode);
		});

		this.setValue(this.parts.input.get().value);
	}

	handleSelectChange(event) {
		this.setCountry(event.target.value);
		const { input } = this.parts;
		if (input) input.get().focus();
	}

	handleInputPaste(event) {
		event.preventDefault();
		this.setValue('');
		const paste = event.clipboardData.getData('text').replace(/[^0-9]/g, '');
		this.setValue(paste);
		this.parts.input.saveCaret();
	}

	handleInputKeyDown(event) {
		const allowedKeys = [...this.options.allowedKeys, ...this.options.metaKeys];
		if (isNaN(event.key) && !(event.ctrlKey || event.metaKey) && !allowedKeys.includes(event.key)) {
			event.preventDefault();
			event.stopPropagation();
			return;
		}
		this.parts.input.saveCaret();
	}

	handleInputKeyUp(event) {
		if (this.options.metaKeys.includes(event.key)) return;
		this.setValue(event.target.value);
		this.parts.input.restoreCaret(event.key);
	}

	handleLabelClick(event) {
		event.preventDefault();
		const { input } = this.parts;
		if (input) input.get().focus();
	}

	setValue(value) {
		this.formatter.reset();
		const { input, placeholder } = this.parts;

		if (value && value !== '(') {
			if (this.checkCallingCode(value)) {
				this.formatter.input(value);

				const phoneNumber = this.formatter.getNumber();
				if (phoneNumber) {
					this.baseElement.value = phoneNumber.format(this.options.numberFormat);
					let inputValue = this.formatter.formattedOutput;
					const oversight = inputValue && this.formatter.formatter.populatedNationalNumberTemplatePosition === -1;
					inputValue =
						!oversight && placeholder.example.startsWith('(') && !this.formatter.formattedOutput.startsWith('(')
							? `(${inputValue}`
							: inputValue;
					input.get().value = inputValue;
					const inputTemplate = this.formatter.formatter.populatedNationalNumberTemplate;
					if (inputTemplate) {
						placeholder.setPlaceholder(inputTemplate.replace(/x/g, '0'), !!inputTemplate);
					} else {
						if (oversight) {
							placeholder.setPlaceholder('', false);
						} else {
							placeholder.setDefaultPlaceholder();
						}
					}
				}
				this.checkValidity();
			}
		} else {
			placeholder.setDefaultPlaceholder();

			if (placeholder.example.startsWith('(')) {
				this.baseElement.value = input.get().value = '(';
			} else {
				this.baseElement.value = input.get().value = '';
			}
		}
	}

	checkCallingCode(value) {
		if (value.startsWith('+') || value.startsWith('00')) {
			const code = value.startsWith('+') ? value.slice(1) : value.slice(2);
			if (code in this.callingCodes) {
				this.parts.input.get().value = '';
				this.setCountry(this.callingCodes[code][0]);
				return false;
			}
		}

		return true;
	}

	checkValidity() {
		const fixElement = this.wrapper.closest('.inp-fix');
		if (fixElement) {
			const phoneNumber = this.formatter.getNumber();
			if (phoneNumber) {
				if (phoneNumber.isValid()) {
					fixElement.classList.add('is-ok');
					fixElement.classList.remove('has-error');

					const errorMessage = fixElement.closest('p').nextElementSibling;
					if (errorMessage && errorMessage.matches('.msg-err')) {
						errorMessage.parentElement.removeChild(errorMessage);
					}
				} else {
					fixElement.classList.remove('is-ok');
					fixElement.classList.add('has-error');
				}
			} else {
				fixElement.classList.remove('is-ok', 'has-error');
			}
		}
	}

	setDefaultValue() {
		if (this.baseElement.value) {
			const phoneNumber = parsePhoneNumberFromString(this.baseElement.value);
			if (typeof phoneNumber !== 'undefined') {
				let country = phoneNumber.country;
				if (!country) {
					country = this.callingCodes[Number(phoneNumber.countryCallingCode)][0];
				}
				if (country) {
					this.setCountry(country);
					this.formatter = new AsYouType(country);
					this.setValue(phoneNumber.nationalNumber);
				}
			}
		}
	}
}
