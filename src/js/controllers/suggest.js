import { Controller } from '@hotwired/stimulus';
import { SuggestMenu } from '../class/Suggest/SuggestMenu';
import { Suggest } from '../class/Suggest/Suggest';

export const create = () => {
	return class extends Controller {
		static targets = ['input', 'wrap', 'button'];

		connect() {
			const suggest = new Suggest(this.inputTarget, {
				minLength: 3,
				typeInterval: 250,
				url: this.element.dataset.suggest,
			});

			const suggestMenu = new SuggestMenu(this.wrapTarget, suggest, {
				item: '.b-suggest__item',
			}).init();

			// menuselect
			suggestMenu.on('click', () => {
				const item = suggestMenu.items[suggestMenu.selectedIndex - 1];

				if (item.classList.contains('f-search__btn')) {
					this.submit();
				} else {
					const link = item.querySelector('a');
					if (link) {
						window.location = link.href;
					}
				}
			});

			// Add click event for button to toggle input visibility
			// this.buttonTarget.addEventListener('click', (e) => {
			// 	e.preventDefault();
			// 	const inputWrap = this.element;
			// 	if (!inputWrap) return;
			// 	inputWrap.classList.toggle('f-search--active');
			// 	this.inputTarget.focus();
			// });
		}
	};
};
