import { Controller } from '@hotwired/stimulus';
import { computePosition, offset, flip, shift, autoUpdate } from '@floating-ui/dom';
import { useClickOutside } from 'stimulus-use';
import { MQ } from '../tools/MQ';

export default class extends Controller {
	static targets = ['submenu', 'trigger'];
	static values = {
		init: String,
	};

	connect() {
		useClickOutside(this);
		this.cleanup = null;
		window.addEventListener('resize', this.onResize);
	}

	disconnect() {
		this.cleanup?.();
		window.removeEventListener('resize', this.onResize);
	}

	clickOutside() {
		this.close();
	}

	toggle(e) {
		e.preventDefault();
		if (this.element.classList.contains('submenu-open')) {
			this.close();
		} else {
			this.open(e);
		}
	}

	open(e) {
		e?.preventDefault();

		if (!this.submenuTarget || !this.triggerTarget) return;

		this.element.classList.add('submenu-open');
		this.submenuTarget.setAttribute('aria-expanded', 'true');

		if (MQ('lsUp')) {
			this.cleanup = autoUpdate(this.triggerTarget, this.submenuTarget, () => {
				computePosition(this.triggerTarget, this.submenuTarget, {
					placement: 'bottom-start',
					middleware: [
						offset(8),
						flip(),
						shift({ padding: 16 }), // Nebo místo var(--grid-gutter) číslo
					],
				}).then(({ x, y }) => {
					Object.assign(this.submenuTarget.style, {
						left: `${x}px`,
						top: `${y}px`,
						position: 'absolute',
						opacity: '1',
						visibility: 'visible',
					});
				});
			});
		} else {
			// Mobil – jen reset stylů, vše se děje přes CSS
			this.submenuTarget.style.left = '';
			this.submenuTarget.style.top = '';
			this.submenuTarget.style.position = '';
			this.submenuTarget.style.opacity = '';
			this.submenuTarget.style.visibility = '';
		}
	}

	close() {
		this.element.classList.remove('submenu-open');
		this.submenuTarget?.setAttribute('aria-expanded', 'false');
		if (MQ('lsUp')) {
			this.submenuTarget?.style.setProperty('opacity', '0');
			this.submenuTarget?.style.setProperty('visibility', 'hidden');
			this.submenuTarget?.style.setProperty('left', '-5000px');
		} else {
			// Mobil – žádné style změny
			if (this.submenuTarget) {
				this.submenuTarget.style.left = '';
				this.submenuTarget.style.top = '';
				this.submenuTarget.style.position = '';
				this.submenuTarget.style.opacity = '';
				this.submenuTarget.style.visibility = '';
			}
		}
		this.cleanup?.();
		this.cleanup = null;
	}

	onResize = () => {
		// Při přepnutí mezi breakpointy zavři submenu (volitelně)
		this.close();
	};
}
