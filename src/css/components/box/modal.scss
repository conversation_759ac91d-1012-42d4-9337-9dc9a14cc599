@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-modal {
	$s: &;
	@include mixins.dialog-reset;
	position: fixed;
	inset: 0;
	z-index: -1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s, z-index 0s 0.3s, visibility 0s 0.3s;
	&__wrapper {
		position: relative;
		z-index: 2;
		display: grid;
		grid-template-rows: 6rem 1fr;
		grid-template-columns: 1fr auto;
		grid-template-areas:
			'content content'
			'content content';
		flex-grow: 1;
		width: 100vw;
		max-width: calc(var(--row-main-width) - (var(--row-main-gutter) * 2));
		max-height: calc(100dvh - var(--row-main-gutter));
		overflow: hidden;
	}
	&__header {
		position: relative;
		z-index: 11;
		display: flex;
		grid-area: 1 / 2 / 2 / 2;
		justify-content: flex-end;
	}
	&__title {
		z-index: 2;
		display: none;
		grid-area: title;
		justify-content: center;
		align-items: center;
		padding: 1rem variables.$row-main-gutter;
		background: variables.$color-white;
		& > * {
			margin: 0;
		}
	}
	&__description {
		display: none;
	}
	&__content {
		position: relative;
		z-index: 2;
		display: flex;
		grid-area: content;
		background: variables.$color-white;
		overflow: hidden;
	}
	&__slide {
		display: flex;
		flex: 0 0 100%;
		align-items: flex-start;
		overflow: hidden;
		overflow-y: auto;
		opacity: 0;
		visibility: hidden;
		transition: opacity variables.$t, visibility variables.$t;
		overscroll-behavior: contain;
		&:not(:first-child) {
			margin-left: -100%;
		}
		&.is-active {
			opacity: 1;
			visibility: visible;
		}
	}
	&__image,
	&__video {
		display: flex;
		justify-content: center;
		align-items: center;
		align-self: center;
		width: 100%;
		height: 100%;
		text-align: center;
		> * {
			width: auto;
			max-width: 100%;
			height: auto;
			max-height: 100%;
			object-fit: contain;
		}
		&--multiple > * {
			max-width: 50%;
		}
	}
	&__image img {
		user-select: none;
	}
	&__inner {
		width: 100%;
		padding: variables.$row-main-gutter;
	}
	&__main {
		padding: 4rem variables.$row-main-gutter;
	}
	&__iframe {
		height: 100%;
		padding: 2rem;
		background: #ffffff;
	}
	&__embed {
		width: 100%;
		height: 100%;
	}
	&__iframe iframe,
	&__embed iframe {
		width: 100%;
		height: 100%;
		aspect-ratio: 16/9;
	}
	&__nav {
		display: none;
		grid-area: nav;
		min-height: 0;
		margin-left: 1rem;
		padding-top: 2rem;
		background: variables.$color-white;
	}
	&__loader {
		position: absolute;
		inset: 0;
		z-index: 10;
		display: none;
		align-items: center;
		justify-items: center;
		color: #ffffff;
	}
	&__loader-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		height: 4rem;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.3);
		line-height: 0;
	}
	&__bg {
		position: absolute;
		inset: 0;
		z-index: 1;
		background-color: rgba(variables.$color-black, 0.8);
	}
	&__prev,
	&__next {
		position: absolute;
		top: 5rem;
		bottom: 0;
		left: 4rem;
		z-index: 3;
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}
	&__next {
		right: 4rem;
		left: auto;
		justify-content: flex-end;
	}
	&__prev-btn,
	&__next-btn {
		@include mixins.button-reset;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 5.6rem;
		height: 5.6rem;
		border: 0.1rem solid variables.$color-bd;
		border-radius: 50%;
		background: variables.$color-white;
		color: variables.$color-black;
		cursor: pointer;
		transition: border-color variables.$t, color variables.$t;
		.icon-svg {
			width: 40%;
			max-width: 2rem;
		}
	}
	&__close {
		@include mixins.button-reset;
		display: flex;
		padding: 2rem;
		color: variables.$color-black;
		outline: none;
		transition: color variables.$t;
		.icon-svg {
			width: 2.5rem;
			height: 2.5rem;
		}
	}

	// Gallery thumbnails
	&__tabs {
		display: grid;
		grid-template-rows: auto 1fr;
		height: 100%;
	}
	&__tab-content {
		position: relative;
		min-height: 0;
		padding: 2rem 2rem 4rem;
		overflow: hidden;
	}
	&__thumbs-wrap {
		height: 100%;
	}
	&__thumbs {
		display: grid;
		grid-template-rows: auto;
		grid-template-columns: 1fr 1fr;
		gap: 0.8rem;
		max-height: 100%;
		margin: -0.5rem;
	}
	&__thumb {
		position: relative;
		cursor: pointer;
		&::before {
			content: '';
			display: block;
			padding-top: 100%;
		}
	}
	&__thumb-img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: 0.2rem solid transparent;
		object-fit: cover;
		transition: border-color variables.$t;
	}
	&__thumbs-prev,
	&__thumbs-next {
		@include mixins.button-reset;
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 4rem;
		background: rgba(variables.$color-white, 0.75);
		transition: opacity variables.$t, visibility variables.$t;
		.icon-svg {
			width: 1.5rem;
		}
		&.is-disabled {
			opacity: 0;
			visibility: hidden;
		}
	}
	&__thumbs-next {
		top: auto;
		bottom: 0;
	}

	// MODIF
	&--8-12 &__wrapper {
		max-width: calc(
			(variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 8 - variables.$row-main-gutter
		);
	}

	// STATES
	body:not(.is-loading) &__inner {
		background: #ffffff;
	}
	&.is-opened {
		z-index: 1000;
		opacity: 1;
		visibility: visible;
		transition: opacity 0.3s, z-index 0s, visibility 0s;
	}
	&.is-loading &__loader {
		display: grid;
	}
	&.is-loading &__loader-icon {
		animation: animation-rotate 0.8s infinite linear;
	}
	&.is-first &__prev,
	&.is-last &__next {
		display: none;
	}

	// HOVERS
	.hoverevents &__close:hover,
	.hoverevents &__prev-btn:hover,
	.hoverevents &__next-btn:hover {
		color: variables.$color-primary;
	}
	&__thumb-img.is-active,
	.hoverevents &__thumb-img:hover {
		border-color: variables.$color-primary;
	}

	// MQ
	@media (config.$md-down) {
		&__wrapper {
			height: 100%;
			max-height: none;
		}
		&__nav {
			display: none;
		}
	}
}
