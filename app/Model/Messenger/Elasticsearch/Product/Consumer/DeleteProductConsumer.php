<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Consumer;

use App\Model\ElasticSearch\Product\ElasticProduct;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Messenger\Elasticsearch\Product\Message\DeleteProductMessage;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class DeleteProductConsumer
{

	public function __construct(
		private EsIndexRepository $esIndexRepository,
		private ProductRepository $productRepository,
		private Service $elasticService,
		private ConsumerHelper $consumerHelper,
	)
	{
	}

	public function __invoke(DeleteProductMessage $message): void
	{
		$this->productRepository->setPublicOnly(false);

		$product = $this->productRepository->getById($message->getId());
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {
			if ($product === null) {
				throw new LogicException(sprintf('Product %s not found', $message->getId()));
			}

			$elasticProduct = new ElasticProduct($product, []);
			$this->elasticService->deleteDoc($esIndex, $elasticProduct);
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), Product::class);
		}
	}

}
