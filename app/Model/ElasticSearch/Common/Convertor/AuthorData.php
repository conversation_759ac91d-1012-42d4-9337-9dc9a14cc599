<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalization;

class AuthorData implements Convertor
{

	public const TYPE = 'author';

	public function convert(object $object): array
	{
		assert($object instanceof AuthorLocalization);

		$now = new \DateTimeImmutable();

		return [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
//			'publicFrom' => $object->publicFrom ? ConvertorHelper::convertTime($object->publicFrom) : new \DateTimeImmutable(),
//			'publicTo' => $object->publicTo ? ConvertorHelper::convertTime($object->publicTo) : (new \DateTimeImmutable())->modify('+100 years'),
			'publicFrom' => ConvertorHelper::convertTime($object->publicFrom ?? $now),
			'publicTo' => ConvertorHelper::convertTime($object->publicTo ?? $now->modify('+100 years')),

		];
	}

}
