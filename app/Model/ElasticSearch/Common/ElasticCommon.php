<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticCommon implements Entity
{

	public const TYPE_TREE = 'tree';
	public const TYPE_BLOG = 'blog';
	public const TYPE_MATERIAL = 'material';
	public const TYPE_FEATURE = 'feature';
	public const TYPE_REFERENCE = 'reference';
	public const TYPE_AUTHOR = 'author';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);
		if (isset($this->object->id)) {
			return match ($class) {
				AuthorLocalization::class => self::TYPE_AUTHOR . '-' . $this->object->id,
				FeatureLocalization::class => self::TYPE_FEATURE . '-' . $this->object->id,
				MaterialLocalization::class => self::TYPE_MATERIAL . '-' . $this->object->id,
				ReferenceLocalization::class => self::TYPE_REFERENCE . '-' . $this->object->id,
				BlogLocalization::class => self::TYPE_BLOG . '-' . $this->object->id,
				Tree::class => self::TYPE_TREE . '-' . $this->object->id,
				CatalogTree::class, CommonTree::class => self::TYPE_TREE . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing common definition for '%s' class", $class))
			};

		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
