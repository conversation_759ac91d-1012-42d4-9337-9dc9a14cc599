<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\Mutation\Mutation;
use Nette\Http\SessionSection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method User|null getById($id)
 * @method ICollection|User[] findByFilter(SessionSection $filter, \App\Model\Security\User $user, string $order)
 */
final class UserRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [User::class];
	}

	public function getByEmail(string $email, Mutation $mutation): ?User
	{
		return $this->getBy([
			'email' => $email,
			'mutations->id' => $mutation->id,
		]);
	}

}
