<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant\Availability;


use App\Infrastructure\Latte\Filters;
use App\Model\DeliveryDate;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Holiday\HolidayModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\Supply;
use App\Model\TranslatorDB;
use App\Utils\DateTime;

final class DefaultProductAvailability extends AbstractProductAvailability
{
	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly HolidayModel $holidayModel,
	)
	{
	}

	public function isShowCartCatalog(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		return $this->product->isInStock && !$this->product->hasPriceFrom($mutation, $priceLevel, $state);
	}

	public function isShowCartDetail(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		if($this->isVariantSetted()) {
			return $this->variant->totalSupplyCount > 0;
		}

		return $this->product->isInStock;
	}

	public function hasPrice(Mutation $mutation, PriceLevel $priceLevel, State $state): bool
	{
		return $this->product->price($mutation, $priceLevel, $state)->isGreaterThan(0);
	}

	public function getMaxAvailableAmount(): int
	{
		if ( ! $this->product->public || ! $this->variant->active) {
			return 0;
		}

		return $this->variant->totalSupplyCount;
	}

	public function getAvailabilityText(): string
	{
		if ($this->variant->isOld) {
			return $this->translate('availability_unavailable');
		} elseif ($this->variant->notSoldSeparately) {
			return $this->translate('not_sold_separately');
		} elseif ($this->variant->isInPrepare) {
			return $this->translate('presale_start');
		} elseif ($this->variant->isInStock) {
			//{translate}{$product->totalSupplyCount|stock}{/translate} ({$product->totalSupplyCount} {_'stock_piece'})
			return $this->translate('availability_in_stock') . ' ' . $this->translate(Filters::stock($this->variant->totalSupplyCount));
		} else {
			return $this->translate('availability_soldout');
		}
	}

	public function getAvailabilityShortText(): string
	{
		return $this->getAvailabilityText();
	}

	public function getDeliveryDate(
		Mutation $mutation,
		State $state,
		?DeliveryMethodConfiguration $deliveryMethodConfiguration = null,
		int $quantityRequired = 1
	): ?DeliveryDate {
		if ($this->variant->totalSupplyCount < $quantityRequired) { // pozadovane mnozstvi neni skladem
			return null;
		}

		return $this->loadCache($this->createCacheKey($this->variant, ... func_get_args()), function () use($state, $deliveryMethodConfiguration, $quantityRequired){
			$dateExpedition = new DateTime();

			if ($quantityRequired <= $this->variant->suplyCountStockDefault) {
				/** @var Supply $supply */
				$supply = $this->variant->suppliesByStockAlias[Stock::ALIAS_SHOP];

				$expedTime = $deliveryMethodConfiguration !== null && isset($deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SHOP}) ? $deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SHOP} : $supply->stock->deliveryHour;
				$dateExpedition->modify($expedTime);
				$dateExpedition->getClosestWorkday();

				if ($dateExpedition->isPast()) {
					$dateExpedition->addWorkday();
				}

			} else {
				/** @var Supply $supply */
				$supply = $this->variant->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE];

				$expedTime = $deliveryMethodConfiguration !== null && isset($deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SUPPLIER_STORE}) ? $deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SUPPLIER_STORE} : $supply->stock->deliveryHour;
				$dateExpedition->modify('today');
				$dateExpedition->modify($expedTime);
				$dateExpedition->addWorkday();

				$supplierStockDays = $supply->deliveryDelay;
				if ($supplierStockDays > 0) {
					$dateExpedition->addWorkday($supplierStockDays);
				}

			}

			/** @var Holiday $holiday */
			foreach ($this->holidayModel->getAll() as $holiday) { // iterujeme chronologicky vsechny platne dovolene
				$holidayEnd = $holiday->publicTo->modify('tomorrow midnight');
				if ($dateExpedition <= $holidayEnd) { // DD je v terminu dovolene
					$dateExpedition = DateTime::from($holidayEnd)->setHolidays($state)->getClosestWorkday(); // DD je nejblizsi prac. den po dovolene
				}
			}

			$dateFrom = clone $dateExpedition;
			$dateTo = null;

			if ($deliveryMethodConfiguration !== null) {
				if (($deliveryDayFrom = $deliveryMethodConfiguration->deliveryDayFrom) > 0) {
					$dateFrom->setHolidays($state);
					$dateFrom->addWorkday($deliveryDayFrom);
				}

				if (($deliveryDayTo = $deliveryMethodConfiguration->deliveryDayTo) > 0) {
					$dateTo = $dateFrom->modifyClone(sprintf('+ %d day', $deliveryDayTo));
					$dateTo->setHolidays($state);
					$dateTo->getClosestWorkday();
				}
			}

			return new DeliveryDate($dateFrom, $dateTo, $dateExpedition);
		});
	}

	public function getExpeditionText(Mutation $mutation, State $state, ?DeliveryMethodConfiguration $deliveryMethodConfiguration = null, int $quantityRequired = 1): ?string
	{
		$deliveryDate = $this->getDeliveryDate($mutation, $state, $deliveryMethodConfiguration, $quantityRequired);
		if ($deliveryDate === null) {
			return null;
		}

		return 'Expedujeme '.$deliveryDate->expedition->format('j. n.');
	}

	public function getDeliveryText(Mutation $mutation, State $state, ?DeliveryMethodConfiguration $deliveryMethodConfiguration = null, int $quantityRequired = 1): ?string
	{
		$deliveryDate = $this->getDeliveryDate($mutation, $state, $deliveryMethodConfiguration, $quantityRequired);
		if ($deliveryDate === null) {
			return null;
		}

		return 'U vás '.$deliveryDate->from->format('j. n.');
	}

	public function getStoreText(State $state): ?string
	{
		// TODO: Implement getStoreText() method.
		return null;
	}

	private function translate(string $key, array $replace = []): string
	{
		if ( ! empty($replace)) {
			return str_replace(array_keys($replace), array_values($replace), $this->translator->translate($key));
		}
		return $this->translator->translate($key);
	}
}
