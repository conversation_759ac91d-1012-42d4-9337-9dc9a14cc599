<?php

declare(strict_types=1);

namespace App\Model\Orm\EmailTemplate;

use App\Model\Orm\EmailTemplateFile\EmailTemplateFile;
use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\Traits\HasFormDefaultData;

/**
 * @property int $id {primary}
 * @property string|null $name {default ''}
 * @property string $key
 * @property int $isDeveloper {default 0}
 * @property int $isHidden {default 0}
 * @property string|null $subject
 * @property string|null $body
 *
 * RELATIONS
 * @property Mutation|null $mutation {m:1 Mutation::$emailTemplates} {default 1}
 * @property EmailTemplateFile[]|OneHasMany $files {1:m EmailTemplateFile::$emailTemplate, orderBy=[sort=ASC], cascade=[persist, remove]}
 *
 */
class EmailTemplate extends Entity
{
	use HasFormDefaultData;

}
