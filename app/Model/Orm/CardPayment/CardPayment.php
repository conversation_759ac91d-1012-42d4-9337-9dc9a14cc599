<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment;

use App\Model\Orm\BackedEnumWrapper;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property-read int $id {primary}
 * @property-read CardPaymentInformation $cardPaymentInformation {m:1 CardPaymentInformation::$payments, cascade=[persist]}
 * @property-read string $paymentGatewayUniqueIdentifier
 * @property-read string $externalId
 * @property-read string|null $externalUrl
 * @property-read CardPaymentStatus $status {wrapper BackedEnumWrapper}
 * @property-read CardPaymentStatusChange[]|OneHasMany $statusChanges {1:m CardPaymentStatusChange::$payment, cascade=[persist, remove]}
 */
final class CardPayment extends Entity
{
	public function __construct(
		CardPaymentInformation $cardPaymentInformation,
		PaymentGateway $gateway,
		string $externalId,
		string|null $externalUrl,
		CardPaymentStatus $initialStatus = CardPaymentStatus::Pending,
	) {
		parent::__construct();
		$this->setReadOnlyValue('cardPaymentInformation', $cardPaymentInformation);
		$this->setReadOnlyValue('paymentGatewayUniqueIdentifier', $gateway->getUniqueIdentifier());
		$this->setReadOnlyValue('externalId', $externalId);
		$this->setReadOnlyValue('externalUrl', $externalUrl);
		$this->setReadOnlyValue('status', $initialStatus);
		$this->statusChanges->add(CardPaymentStatusChange::of($this, null, $initialStatus));
	}

	public function updateStatus(CardPaymentStatus $status): void
	{
		$this->statusChanges->add(CardPaymentStatusChange::of($this, $this->status, $status));
		$this->setReadOnlyValue('status', $status);
	}
}
