<?php declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasPublicParameter;

/**
 * @method State getById($id)
 */
final class StateRepository extends Repository implements Searchable, CollectionById
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [State::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}


	public function getDefault(Mutation $mutation): ?State
	{
		$defaultState = $this->findBy(['mutations->id' => $mutation->id])->fetch();
		if ($defaultState === null) {
			$defaultState = $this->getBy(['code' => State::DEFAULT_CODE]);
		}
		return $defaultState;
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): StateMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof StateMapper);
		return $mapper;
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->$this->getMapper()->findByIdOrder($ids);
	}


}
