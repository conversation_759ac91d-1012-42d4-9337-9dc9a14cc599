<?php declare(strict_types=1);

namespace App\Model\Orm\Mutation;

use App\Model\Orm\Orm;

final class MutationModel
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	public function findForRouter(string $domain, string $prefix = ''): ?Mutation
	{
		static $cache = [];
		if (!isset($cache['routerInfo'])) {

			$mutations = $this->orm->mutation->findBy([
				'public' => 1
			])->fetchAll();
			$tmp = [];
			foreach ($mutations as $mutation) {
				$tmp[strtolower($mutation->getRealDomainWithoutWWW()) . '--' . strtolower($mutation->getRealUrlPrefix())] = $mutation;
			}

			$cache['routerInfo'] = $tmp;
		}

		$key = strtolower($domain) . '--' . strtolower($prefix);
		if (isset($cache['routerInfo'][$key])) {
			return $cache['routerInfo'][$key];
		}

		return null;
	}


	public function delete(Mutation $mutation): void
	{
		$this->orm->mutation->removeAndFlush($mutation);
	}

}
