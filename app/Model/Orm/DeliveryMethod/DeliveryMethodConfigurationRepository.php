<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Brick\Money\Money;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method DeliveryMethodConfiguration|null getFreeDelivery(Mutation $mutation, State $state, PriceLevel $priceLevel)
 * @method Money|null getFreeDeliveryAmount(Mutation $mutation, State $state, PriceLevel $priceLevel)
 * @method DeliveryMethodConfiguration[]|ICollection getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel)
 */
final class DeliveryMethodConfigurationRepository extends Repository
{
	public static function getEntityClassNames(): array
	{
		return [DeliveryMethodConfiguration::class];
	}
}
