<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\BigDecimalContainer;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Brick\Math\BigDecimal;
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 *
 * @property DeliveryMethodConfiguration $deliveryMethod {m:1 DeliveryMethodConfiguration::$prices}
 *
 * @property PriceLevel $priceLevel {m:1 PriceLevel, oneSided=true}
 * @property State $state {m:1 State, oneSided=true}
 *
 * @property Price $price {embeddable}
 * @property BigDecimal|null $freeFrom {wrapper BigDecimalContainer} {default null}
 * @property int|null $maxWeight
 * @property int|null $maxCodPrice
 */
final class DeliveryMethodPrice extends Entity
{

}
