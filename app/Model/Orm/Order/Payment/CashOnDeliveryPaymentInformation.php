<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

final class CashOnDeliveryPaymentInformation extends PaymentInformation
{
	protected function getType(): PaymentType
	{
		return PaymentType::CashOnDelivery;
	}

	protected function getInitialState(): PaymentState
	{
		return PaymentState::Completed;
	}

	public function isOnline(): bool
	{
		return false;
	}

	public function redirectUrl(): ?string
	{
		return null;
	}

	public function getState(): ?PaymentState
	{
		return null;
	}
}
