<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductLocalization|null getById($id)
 * @method ProductLocalization save(?ProductLocalization $entity, array $data)
 */
final class ProductLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;
	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [ProductLocalization::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		$ret = [
			'public' => 1,
			'mutation' => $orm->getMutation(),
		];

		$now = $this->getNowDateTime();

		$ret['product->publicFrom<='] = $now;
		$ret['product->publicTo>='] = $now;

		return $ret;
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ProductLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
