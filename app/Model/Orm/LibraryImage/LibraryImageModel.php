<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Image\ImageHelper;
use App\Model\Image\Storage\BasicStorage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Utils\DirectoryUtils;
use App\Model\Utils\FilenameUtils;
use Nette\Http\FileUpload;
use Nette\Utils\ImageException;
use Nette\Utils\Strings;
use Nette\Utils\UnknownImageFileException;
use Tracy\Debugger;

final readonly class LibraryImageModel
{
	public function __construct(
		private Orm           $orm,
		private ConfigService $configService,
		private BasicStorage  $imageStorage,
		private Repository  $esAllRepository,
	)
	{
	}

	public function addFromFileUpload(FileUpload $file, int $cat, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$name = $file->getUntrustedName();
		$image = $this->addFromTmpFile($file->getTemporaryFile(), $cat, $name, $isCopy, $sourceImage, $md5);
		$this->orm->flush();
		return $image;
	}

	public function addFromTmpFile(string $file, ?int $cat, ?string $name = null, bool $isCopy = false, ?string $sourceImage = null, ?string $md5 = null): LibraryImage
	{
		$fileName = $name ?? basename($file);
		$name = Strings::substring($fileName, 0, Strings::indexOf($fileName, '.', -1));

		$newImage = new LibraryImage();
		$newImage->name = $name;
		$newImage->library = $this->orm->libraryTree->getById($cat);

		$this->orm->libraryImage->persist($newImage);

		$filename = $newImage->id . '-' . FilenameUtils::sanitize($fileName);
		$pathInfo = pathinfo($filename);
		$filenameWithoutExtension = $pathInfo['filename'];
		$extension = $pathInfo['extension'] ?? '';

		$originalPath = $this->imageStorage->getPathToOriginalImage($filenameWithoutExtension, $extension);
		DirectoryUtils::ensureDirectoryExists(dirname($originalPath));

		if (!copy($file, $originalPath)) {
			throw new \RuntimeException(sprintf('Failed to copy file from "%s" to "%s"', $file, $originalPath));
		}

		if (!$isCopy && $file !== ($originalPath)) {
			unlink($file);
		}

		try{
			[$width, $height] = ImageHelper::resolveImageSize($this->configService, $originalPath);
			$newImage->width = $width;
			$newImage->height = $height;
		}catch (UnknownImageFileException|ImageException|LogicException $e) {
			Debugger::log($e);
		}

		$newImage->filename = $filename;
		$newImage->sourceImage = $sourceImage;
		$newImage->md5 = $md5;
		$newImage->sort = -$newImage->id;

		$this->orm->libraryImage->persist($newImage);

		return $newImage;
	}

	public function recalculateSizes(LibraryImage $libraryImage): array
	{
		$pathInfo = pathinfo($libraryImage->filename);
		$originalPath = $this->imageStorage->getPathToOriginalImage($pathInfo['filename'], $pathInfo['extension'] ?? '');

		try{
			[$width, $height] = ImageHelper::resolveImageSize($this->configService, $originalPath);
			$libraryImage->width = $width;
			$libraryImage->height = $height;

			$this->orm->libraryImage->persistAndFlush($libraryImage);

			return [$width, $height];
		}catch (UnknownImageFileException|ImageException|LogicException $e) {
			Debugger::log($e);
			return [$libraryImage->width, $libraryImage->height];
		}

	}

    public function deleteImage(LibraryImage $image): void
    {
		$fileInfo = pathinfo($image->filename);

		if (isset($fileInfo['extension'])) {
			$this->imageStorage->deleteOriginal($fileInfo['filename'], $fileInfo['extension']);
			$this->imageStorage->deleteAllSizes($fileInfo['filename'], $fileInfo['extension']);
		}

		$this->orm->libraryImage->removeAndFlush($image);
    }

	public function getImageUsage(LibraryImage $image, Mutation $mutation): array
	{
		$esIndex = $this->orm->esIndex->getAllLastActive($mutation);
		$usage = [];
		if ($esIndex !== null) {
			$usage = array_map(
				static function (\stdClass $data) {
					return $data->name;
				},
				$this->esAllRepository->searchImageUsage($esIndex, $image)
			);
		}
		return $usage;
	}

}
