<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\PostType\Page\Model\Orm\Tree;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

final class QueryBaseFilter
{

	public function get(array $baseItems, string $fulltext = null): AbstractQuery
	{
		$b = new QueryBuilder();
		$query = $b->query()->bool();

		foreach ($baseItems as $baseItem) {
			$condition = $baseItem->getCondition();
			if ($condition !== null) {
				$query->addMust($baseItem->getCondition());
			}
		}

		if ($fulltext) {

			$fields = [];
			$fields[] = 'name.dictionary^80';
			$fields[] = 'annotation.dictionary^40';
			$fields[] = 'content.dictionary^40';

			$b = new QueryBuilder();

			$query->addMust( $b->query()->multi_match()
				->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
				->setQuery($fulltext)
				->setFuzziness(3)
				->setOperator('OR')
				->setFields($fields)
			);
		}

		return $query;
	}

}
