<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;

final class SortCreator
{

	public function create(string $order, State $state, PriceLevel $priceLevel): Sort
	{
		$sort = new Sort();
		$sort->addByStore();
		if ($order === 'top') {
			$sort->addByTopScore($state, $priceLevel);
		} elseif ($order === 'score') {
            $sort->addByScore();
		} elseif ($order === 'bestseller') {
			$sort->addByBestsellere();
		} elseif ($order === 'cheapest') {
			$sort->addByPrice($state, $priceLevel, 'asc');
		} elseif ($order === 'expensive') {
			$sort->addByPrice($state, $priceLevel, 'desc');
		} elseif ($order === 'review') {
			$sort->addByReview();
		}

		$sort->addByName();

		return $sort;
	}

}
