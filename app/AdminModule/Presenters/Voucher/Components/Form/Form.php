<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Voucher\Components\Form;

use App\AdminModule\Presenters\Voucher\Components\Form\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use const RS_TEMPLATE_DIR;

/**
 * @property-read DefaultTemplate $template
 */
final class Form extends Control
{
	public function __construct(
		private readonly Voucher $voucher,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	) {}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('voucher', $this->voucher);
		$template->add('imgSrc', '');
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->currency = $this->voucher->mutation->currency->getCurrencyCode();


		$template->showDeleteButton = !$this->voucher->isUsed && !$this->voucher->isInOrder;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->voucher, $this->userEntity);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($form, $this->voucher, $data, $this->userEntity);
		$this->presenter->redirect('edit', ['id' => $this->voucher->id]);
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDelete(): void
	{
		$this->orm->removeAndFlush($this->voucher);
		$this->presenter->flashMessage('msg_ok_deleted', 'ok');
		$this->presenter->redirect('default');
	}

	public function handleExport(): void
	{
		/** @var ICollection<VoucherCode> $codes */
		$codes = $this->orm->voucherCode->findBy([
			'isUsed' => 0,
			'voucher' => $this->voucher,
		]);

		$csvData = [];

		/** @var VoucherCode $voucherCode */
		foreach ($codes as $voucherCode) {
			$csvLine = [];
			$csvLine[] = $voucherCode->code;
			$csvData[] = $csvLine;
		}

		$fileContent = $this->prepareCsvFileContent($csvData);
		$this->sendFileResponse($fileContent);
	}


	public function handleExportAll(): void
	{
		/** @var ICollection<VoucherCode> $codes */
		$codes = $this->orm->voucherCode->findBy([
			'voucher' => $this->voucher,
		]);

		$csvData = [];

		/** @var VoucherCode $voucherCode */
		foreach ($codes as $voucherCode) {
			$csvLine = [];

			$csvLine[] = $voucherCode->code;
			$csvLine[] = $voucherCode->createdTime->format('j. n. Y H:i:s');
			$csvLine[] = $this->translator->translate(
				$voucherCode->isUsed
					? 'voucher_code_used'
					: 'voucher_code_active',
			);

			$csvData[] = $csvLine;
		}

		$fileContent = $this->prepareCsvFileContent($csvData);
		$this->sendFileResponse($fileContent);
	}


	private function prepareCsvFileContent(array $csvData): string
	{
		$fileContent = '';

		foreach ($csvData as $line) {
			$processedLine = is_array($line)
				? implode(';', $line)
				: (string) $line;

			$fileContent .= $processedLine . "\n";
		}

		return $fileContent;
	}


	private function sendFileResponse(string $fileContent, string $fileName = 'export.csv'): void
	{
		header('Pragma:  no-cache');
		header('Expires: 0');
		header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
		header('Cache-Control: private', false);
		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment; filename="' . $fileName . '"');
		header('Content-Transfer-Encoding: binary');

		if (empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
			// don't use length if server using compression
			header('Content-Length: ' . mb_strlen($fileContent, 'UTF-8'));
		}

		$fileContent = iconv('UTF-8', 'windows-1250', $fileContent);

		echo $fileContent;

		$this->presenter->terminate();
	}


}
