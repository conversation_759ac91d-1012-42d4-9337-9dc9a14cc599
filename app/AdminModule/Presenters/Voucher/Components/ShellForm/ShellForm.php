<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Voucher\Components\ShellForm;


use App\AdminModule\Presenters\Voucher\Components\ShellForm\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Price;
use App\Model\Orm\User\User;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\Voucher\VoucherRepository;
use App\Model\Translator;
use Brick\Money\Money;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Random;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
class ShellForm extends Control
{

	/**
	 * @var ICollection<Mutation>
	 */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly ConfigService $configService,
		private readonly VoucherRepository $voucherRepository,
		private readonly User $userEntity,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSelect('type', 'label_voucherType', $this->configService->get('voucherTypes'))->setRequired('Type is required');
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}



	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$voucher = new Voucher();
		$this->voucherRepository->attach($voucher);
		$voucher->mutation = $data->mutation;
		$voucher->created = $this->userEntity->id;
		$voucher->type = $data->type;
		$voucher->discount = Price::from(Money::of(0, $voucher->mutation->currency->getCurrencyCode()));

		$this->voucherRepository->persistAndFlush($voucher);

		$this->presenter->redirect('edit', ['id' => $voucher->id]);
	}

}
