<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order\Components\DataGrid;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class DataGrid extends Control
{
	use HasMutationColumn;

	public function __construct(
		private readonly ICollection $orders,
		private readonly Translator $translator,
		private readonly Orm $orm,
	) {}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->orders);
		$grid->addColumnText('id', 'id');

		$this->addColumnMutation($grid);

		$grid->addColumnText('state', 'order_state')
			->setRenderer(fn(Order $order) => $order->state->value);

		$grid->addColumnText('orderNumber', 'order_number')
			->setSortable()
			->setFilterText();

		$grid->addColumnDateTime('placedAt', 'placedAt')
			->setSortable();

		$grid->addColumnText('name', 'order_customer_name');

		$grid->addColumnNumber('price', 'order_price')
			->setRenderer(fn(Order $order) => $order->getTotalPriceVat(withDelivery: true)->formatTo('cs'))
			->setSortable();

		$grid->addAction('detail', 'Detail', ':detail')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}
}
