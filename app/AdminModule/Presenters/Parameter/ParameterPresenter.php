<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Parameter\Components\EditForm\EditForm;
use App\AdminModule\Presenters\Parameter\Components\EditForm\EditFormFactory;
use App\AdminModule\Presenters\Parameter\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Parameter\Components\ShellForm\ShellFormFactory;
use App\AdminModule\Presenters\Parameter\Components\ValueEditForm\ValueEditForm;
use App\AdminModule\Presenters\Parameter\Components\ValueEditForm\ValueEditFormFactory;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;

final class ParameterPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'parameter';

	public ?Parameter $parameter = null;

	private ?ParameterValue $parameterValue;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly EditFormFactory $editFormFactory,
		private readonly ValueEditFormFactory $valueEditFormFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
	}

	public function actionEdit(int $id): void
	{
		$this->parameter = $this->orm->parameter->getById($id);

		if ($this->parameter === null) {
			$this->redirect('default');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function renderEdit(int $id): void
	{
		$this->template->parameter = $this->parameter;
	}

	public function actionEditValue(int $parameterId, int $id): void
	{
		$this->parameter = $this->orm->parameter->getById($parameterId);
		$this->parameterValue = $this->orm->parameterValue->getById($id);

		if ($this->parameter === null) {
			$this->redirect('default');
		}

		if ($this->parameterValue === null) {
			$this->redirect('edit', ['id' => $this->parameter->id]);
		}
	}

	public function renderEditValue(): void
	{
		$this->template->parameter = $this->parameter;
		$this->template->parameterValue = $this->parameterValue;
	}

	protected function createComponentParameterForm(): EditForm
	{
		return $this->editFormFactory->create($this->parameter, $this->userEntity);
	}

	protected function createComponentParameterValueForm(): ValueEditForm
	{
		return $this->valueEditFormFactory->create($this->parameterValue);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->parameter->findAll());
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
