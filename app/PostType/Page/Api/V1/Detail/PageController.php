<?php

declare(strict_types=1);

namespace App\PostType\Page\Api\V1\Detail;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\V1\Controllers\BaseV1Controller;
use App\PostType\Page\Api\V1\Detail\Response\Page;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use function assert;
use function sprintf;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Page')]
final class PageController extends BaseV1Controller
{

	public function __construct(
		private readonly TreeRepository $treeRepository,
	)
	{
	}

	#[Path('/mutation/{mutationId}/page/{id}')]
	#[Method('GET')]
	#[RequestParameter(name: 'id', type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Page ID')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Success', code: '200', entity: Page::class)]
	#[Response(description: 'Not found', code: '404')]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$tree = $this->treeRepository->getBy([
			'id' => $request->getParameter('id'),
			'mutation' => $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME),
		]);

		if ($tree === null) {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}

		assert($tree instanceof Tree);
		return $this->jsonResponse(
			(new Page($tree))->toResponse(),
			$response,
		);
	}

	#[Path('/mutation/{mutationId}/page')]
	#[Method('GET')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Redirect to root page', code: '301')]
	public function getRoot(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$root = $this->treeRepository->getBy([
			'mutation' => $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME),
			'parentId' => null,
		]);

		assert($root !== null);

		return $response
			->withStatus(ApiResponse::S301_MOVED_PERMANENTLY)
			->withHeader('Location', sprintf(
				'/api/v1/mutation/%d/page/%d',
				$request->getParameter(MutationFinder::MUTATION_ID_PARAMETER_NAME),
				$root->id,
			));
	}

}
