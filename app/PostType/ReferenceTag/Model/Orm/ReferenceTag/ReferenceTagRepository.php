<?php declare(strict_types = 1);

namespace App\PostType\ReferenceTag\Model\Orm\ReferenceTag;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use App\PostType\Author\Model\Orm\AuthorMapper;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ReferenceTag getById($id)
 * @method ReferenceTag[]|ICollection findByExactOrder(array $ids)
 */
final class ReferenceTagRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [ReferenceTag::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): ReferenceTagMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof ReferenceTagMapper);
		return $mapper;
	}

}
