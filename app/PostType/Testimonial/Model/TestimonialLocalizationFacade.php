<?php declare(strict_types = 1);

namespace App\PostType\Testimonial\Model;

use App\PostType\Testimonial\Model\Orm\Testimonial;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class TestimonialLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): TestimonialLocalization
	{
		$testimonialLocalization = new TestimonialLocalization();
		$this->orm->testimonialLocalization->attach($testimonialLocalization);
		$testimonialLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Testimonial();
			$testimonialLocalization->testimonial = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Testimonial);
			$testimonialLocalization->testimonial = $localizableEntity;
		}

		$this->orm->persistAndFlush($testimonialLocalization);

		return $testimonialLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof TestimonialLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Testimonial);
		$this->orm->testimonialLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->testimonial->remove($parent);
		}

		$this->orm->flush();
	}

}
