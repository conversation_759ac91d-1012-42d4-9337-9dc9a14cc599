<?php

namespace App\PostType\Testimonial\AdminModule\Components\DataGrid;

use App\Model\Translator;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

class TestimonialDataGridPrescription
{

	public function __construct(
//		private readonly Translator $translator,
	)
	{
	}

	public function get(): DataGridDefinition {
		return new DataGridDefinition(
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->addIsTop(...)
				),
			]
		);
	}

	private function addIsTop(DataGrid $dataGrid): void
	{
//		$dataGrid->addColumnText('isTop', 'isTop')
//		->setRenderer(function (TestimonialLocalization $testimonialLocalization) {
//			return ($testimonialLocalization->isTop) ?
//				$this->translator->translate('top') :
//				$this->translator->translate('common');
//		})
//		->setFilterSelect([
//			true => $this->translator->translate('top'),
//			false => $this->translator->translate('common'),
//		])->setPrompt($this->translator->translate('all'));
	}
}
