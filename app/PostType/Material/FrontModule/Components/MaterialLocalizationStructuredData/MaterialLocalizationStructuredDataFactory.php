<?php declare(strict_types = 1);

namespace App\PostType\Material\FrontModule\Components\MaterialLocalizationStructuredData;

use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\Model\Orm\Mutation\Mutation;

interface MaterialLocalizationStructuredDataFactory
{

	public function create(
		MaterialLocalization $materialLocalization,
		Mutation $mutation,
	): MaterialLocalizationStructuredData;

}


