<?php

namespace App\PostType\Feature\AdminModule\Components\DataGrid;

use App\Model\Translator;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

class FeatureDataGridPrescription
{

	public function __construct(
		private readonly Translator $translator,
	)
	{
	}

	public function get(): DataGridDefinition {
		return new DataGridDefinition(
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->addIsTop(...)
				),
			]
		);
	}

	private function addIsTop(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('isTop', 'isTop')
		->setRenderer(function (FeatureLocalization $featureLocalization) {
			return ($featureLocalization->isTop) ?
				$this->translator->translate('top') :
				$this->translator->translate('common');
		})
		->setFilterSelect([
			true => $this->translator->translate('top'),
			false => $this->translator->translate('common'),
		])->setPrompt($this->translator->translate('all'));
	}
}
