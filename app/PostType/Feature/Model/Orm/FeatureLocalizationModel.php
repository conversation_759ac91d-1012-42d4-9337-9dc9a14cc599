<?php declare(strict_types = 1);

namespace App\PostType\Feature\Model\Orm;

use Jaybizzle\CrawlerDetect\CrawlerDetect;

class FeatureLocalizationModel
{

	public function __construct(
		private readonly FeatureLocalizationRepository $featureLocalizationRepository,
		private readonly FeatureLocalizationTreeRepository $featureLocalizationTreeRepository,
	)
	{}


	public function increaseViews(FeatureLocalization $featureLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$featureLocalization->viewsNumber++;
		$this->featureLocalizationRepository->persistAndFlush($featureLocalization);
		return true;
	}

	/**
	 * @param array<int, int> $treeIds
	 */
	public function setCategoriesByIds(FeatureLocalization $featureLocalization, array $treeIds): void
	{
		$currentFeatureLocalizationTrees = $this->featureLocalizationTreeRepository->findBy(['featureLocalization' => $featureLocalization])->fetchPairs('tree->id');
		$sort = 0;
		foreach ($treeIds as $treeId) {

			if (isset($currentFeatureLocalizationTrees[$treeId])) {
				// update sort
				$featureLocalizationTree = $currentFeatureLocalizationTrees[$treeId];
				assert($featureLocalizationTree instanceof FeatureLocalizationTree);
				$featureLocalizationTree->sort = $sort;
				// unset array
				unset($currentFeatureLocalizationTrees[$treeId]);
			} else {
				// create new
				$featureLocalizationTree = new FeatureLocalizationTree();
				$featureLocalizationTree->featureLocalization = $featureLocalization;
				$featureLocalizationTree->tree = $treeId;
				$featureLocalizationTree->sort = $sort;
			}
			$sort++;
		}

		// remove old
		if ($currentFeatureLocalizationTrees !== []) {
			foreach ($currentFeatureLocalizationTrees as $currentFeatureLocalizationTree) {
				$this->featureLocalizationTreeRepository->remove($currentFeatureLocalizationTree);
			}
		}
	}

}
