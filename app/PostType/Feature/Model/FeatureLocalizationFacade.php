<?php declare(strict_types = 1);

namespace App\PostType\Feature\Model;

use App\PostType\Feature\Model\Orm\Feature;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class FeatureLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): FeatureLocalization
	{
		$featureLocalization = new FeatureLocalization();
		$this->orm->featureLocalization->attach($featureLocalization);
		$featureLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Feature();
			$featureLocalization->feature = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Feature);
			$featureLocalization->feature = $localizableEntity;
		}

		$this->orm->persistAndFlush($featureLocalization);

		return $featureLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof FeatureLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Feature);
		$this->orm->featureLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->feature->remove($parent);
		}

		$this->orm->flush();
	}

}
