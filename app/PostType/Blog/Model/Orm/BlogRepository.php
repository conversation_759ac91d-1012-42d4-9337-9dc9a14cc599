<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use App\PostType\Testimonial\Model\Orm\Testimonial;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Blog getById($id)
 * @method Blog[]|ICollection searchByName(string $q, array $excluded = [])
 * @method Blog[]|ICollection findByExactOrder(array $ids)
 */
final class BlogRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Blog::class];
	}


	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): BlogMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof BlogMapper);
		return $mapper;
	}

}
