<?php declare(strict_types=1);

namespace App\PostType\Blog\Model;

use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\PostType\Blog\Model\Dto\BlogLocalizationDto;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use Nette\Http\FileUpload;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Symfony\Component\Console\Output\OutputInterface;
use Tracy\Debugger;

final readonly class BlogImportService
{
	public function __construct(
		private Orm $orm,
		private LibraryImageModel $libraryImageModel,
		private BlogLocalizationFacade $blogLocalizationFacade,
		private CustomFields $customFields,
		private CustomContent $customContent,
	) {
	}

	/**
	 * Import blogs from parsed JSON data
	 *
	 * @param array $blogData Array of parsed blog objects
	 * @param Mutation $mutation Target mutation
	 * @param User|null $user User performing the import
	 * @param string $libraryTreeUID Library tree UID for storing images
	 * @return array Import results with statistics
	 */
	public function importBlogs(OutputInterface $output, array $blogData, Mutation $mutation, ?User $user = null, string $libraryTreeUID = "imported-images"): array
	{
		$results = [
			'total' => count($blogData),
			'imported' => 0,
			'skipped' => 0,
			'errors' => [],
			'imported_blogs' => [],
		];

		$libraryTree = $this->orm->libraryTree->getByUid($libraryTreeUID);
		if (!$libraryTree) {
			throw new \InvalidArgumentException("Library tree with uid {$libraryTreeUID} not found");
		}

		foreach ($blogData as $index => $blogItem) {
			try {
				if ($this->orm->blogLocalization->getBy([
						'nameAnchor' => $blogItem['slug'],
						'mutation' => $mutation,
					]) !== null) {
					$results['skipped']++;
					$output->writeln("Skipped blog: {$blogItem['title']} (already exists)");
					continue;
				}

				$blog = $this->importSingleBlog($output, $blogItem, $mutation, $user, $libraryTree);
				$results['imported']++;
				$results['imported_blogs'][] = [
					'id' => $blog->id,
					'title' => $blog->name,
					'slug' => $blog->nameAnchor,
				];

				$output->writeln("Imported blog: {$blog->name}");

				break;
			} catch (\Exception $e) {
				$results['errors'][] = [
					'index' => $index,
					'title' => $blogItem['title'] ?? 'Unknown',
					'error' => $e->getMessage(),
				];
				$results['skipped']++;
				$output->writeln($e->getMessage());
			}
		}

		return $results;
	}

	/**
	 * Import single blog from data
	 *
	 * @param array $blogItem Blog item data
	 * @param Mutation $mutation Target mutation
	 * @param ?User $user User performing the import
	 * @param LibraryTree $libraryTree Library tree for images
	 * @return BlogLocalization Created blog localization
	 */
	private function importSingleBlog(OutputInterface $output, array $blogItem, Mutation $mutation, ?User $user, LibraryTree $libraryTree): BlogLocalization
	{

		$blog = new Blog();
		$blog->internalName = $blogItem['title'];

		$blogCustomFields = [];

		if (!empty($blogItem['image'])) {
			try {
				$blogCustomFields['base'] = [['mainImage' => (string)$this->downloadAndSaveImage($blogItem['image'], $libraryTree)->id]];
			} catch (\Exception $e) {

				$output->writeln("Failed to download image for blog '{$blogItem['title']}': " . $e->getMessage());
			}
		}
		$blog->setCf($this->customFields->prepareDataToSave(Json::encode($blogCustomFields)));
		$this->orm->blog->persist($blog);

		$blogLocalization = $this->blogLocalizationFacade->create($mutation, $blog);

		$blogTags = [];
		if (!empty($blogItem['tags'])) {
			$blogTags = $this->getOrCreateTags($blogItem['tags'], $mutation, $user);
		}

		$authors = [];
		if (!empty($blogItem['author'])) {
			$authorLocalization = $this->getOrCreateAuthor($blogItem['author'], $mutation, $user);
			if ($authorLocalization) {
				$authors[] = $authorLocalization->author;
			}
		}

		$publicDate = DateTimeImmutable::createFromFormat('Y-m-d', $blogItem['date']);

		$cc = [];
		if(isset($blogItem['content_html']) && $blogItem['content_html']) {
			$cc = ['content____' . Random::generate(20) => [['content' => $blogItem['content_html']]]];
		}

		$dto = new BlogLocalizationDto(
			isPublic: true,
			name: $blogItem['title'],
			nameAnchor: $blogItem['slug'],
			nameTitle: $blogItem['title'],
			editor: $user,
			alias: $blogItem['slug'],
			cf: ArrayHash::from([]),
			cc: ArrayHash::from($cc),
			commonCf: null,
			description: $blogItem['content_html'],
			tags: $blogTags,
			authors: $authors,
			categories: new EmptyCollection(),
		);

		if ($publicDate) {
			$blogLocalization->publicFrom = $publicDate;
		}

		$blogLocalization = $this->blogLocalizationFacade->put($blogLocalization, $dto);

		if(isset($blogItem['annotation']) && $blogItem['annotation']) {
			$blogLocalization->setCf($this->customFields->prepareDataToSave(Json::encode(['base' => [['annotation' => $blogItem['annotation']]]])));
		}


		$blogLocalization->setCc(Json::decode(Json::encode($cc)));
		$this->orm->blogLocalization->persistAndFlush($blogLocalization);

		return $blogLocalization;
	}

	private function downloadAndSaveImage(string $imageUrl, LibraryTree $libraryTree): LibraryImage
	{
		if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
			throw new \InvalidArgumentException("Invalid image URL: {$imageUrl}");
		}

		return $this->libraryImageModel->addFromFileUpload(
			$this->urlImgToFileUpload($imageUrl),
			12,
		);
	}

	/**
	 * Get or create blog tags
	 *
	 * @param array $tagNames Array of tag names
	 * @param Mutation $mutation Target mutation
	 * @param User|null $user User creating the tags
	 * @return array Array of BlogTag entities
	 */
	private function getOrCreateTags(array $tagNames, Mutation $mutation, ?User $user): array
	{
		$tags = [];

		foreach ($tagNames as $tagName) {
			if (!is_string($tagName) || trim($tagName) === '') {
				continue;
			}

			$tagName = trim($tagName);
			$tagSlug = Strings::webalize($tagName);

			$existingTagLocalization = $this->orm->blogTagLocalization->getBy([
				'nameAnchor' => $tagSlug,
				'mutation' => $mutation,
			]);

			if ($existingTagLocalization) {
				$tags[] = $existingTagLocalization->blogTag;
				continue;
			}

			$blogTag = new BlogTag();
			$blogTag->internalName = $tagName;
			$blogTag->type = BlogTag::TYPE_BLOG;
			$this->orm->blogTag->persist($blogTag);

			$blogTagLocalization = new BlogTagLocalization();
			$blogTagLocalization->blogTag = $blogTag;
			$blogTagLocalization->mutation = $mutation;
			$blogTagLocalization->name = $tagName;
			$blogTagLocalization->nameAnchor = $tagSlug;
			$blogTagLocalization->nameTitle = $tagName;
			$blogTagLocalization->public = true;
			$blogTagLocalization->edited = $user->id ?? null;
			$blogTagLocalization->editedTime = new DateTimeImmutable();
			$this->orm->blogTagLocalization->persist($blogTagLocalization);

			$tags[] = $blogTag;
		}

		return $tags;
	}

	/**
	 * Get or create author from import data
	 *
	 * @param array $authorData Author data with 'id' and 'name' fields
	 * @param Mutation $mutation Target mutation
	 * @param User|null $user User performing the import
	 * @return AuthorLocalization|null Created or found author localization
	 */
	private function getOrCreateAuthor(array $authorData, Mutation $mutation, ?User $user): ?AuthorLocalization
	{
		if (empty($authorData['name'])) {
			return null;
		}

		$authorName = trim($authorData['name']);

		$existingAuthorLocalization = $this->orm->authorLocalization->getBy([
			'nameAnchor' => $authorName,
			'mutation' => $mutation,
		]);

		if ($existingAuthorLocalization) {
            assert($existingAuthorLocalization instanceof AuthorLocalization);
			return $existingAuthorLocalization;
		}

		$author = new Author();
		$author->internalName = $authorName;
		$this->orm->author->persist($author);

		$authorLocalization = new AuthorLocalization();
		$authorLocalization->author = $author;
		$authorLocalization->mutation = $mutation;
		$authorLocalization->name = $authorName;
		$authorLocalization->nameAnchor = $authorName;
		$authorLocalization->nameTitle = $authorName;
		$authorLocalization->public = true;
		$authorLocalization->edited = $user->id ?? null;
		$authorLocalization->editedTime = new DateTimeImmutable();
		$this->orm->authorLocalization->persist($authorLocalization);

		return $authorLocalization;
	}

	/**
	 * @param string $imageUrl
	 * @return FileUpload
	 * @throws \RuntimeException
	 */
	private function urlImgToFileUpload(string $imageUrl): FileUpload
	{
		// Create context with proper headers and SSL settings
		$context = stream_context_create([
			'http' => [
				'timeout' => 30,
				'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
				'method' => 'GET',
				'header' => [
					'Accept: image/webp,image/apng,image/*,*/*;q=0.8',
					'Accept-Language: en-US,en;q=0.9',
					'Accept-Encoding: gzip, deflate, br',
					'Referer: https://www.sedlakovalegal.cz/',
					'Connection: keep-alive',
				],
			],
			'ssl' => [
				'verify_peer' => false,
				'verify_peer_name' => false,
			]
		]);

		$imageContent = file_get_contents($imageUrl, false, $context);

		if ($imageContent === false) {
			throw new \RuntimeException("Failed to download image from URL: " . $imageUrl);
		}

		$tempDir = sys_get_temp_dir();
		$originalFilename = basename($imageUrl);
		$extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
		$tempFilename = 'nette_upload_' . Random::generate(10) . '.' . $extension;
		$tempFilePath = $tempDir . DIRECTORY_SEPARATOR . $tempFilename;

		if (file_put_contents($tempFilePath, $imageContent) === false) {
			throw new \RuntimeException("Failed to save image to temporary file: " . $tempFilePath);
		}

		$mimeType = mime_content_type($tempFilePath);
		$fileSize = filesize($tempFilePath);

		$fileData = [
			'name' => $originalFilename,
			'type' => $mimeType,
			'tmp_name' => $tempFilePath,
			'error' => UPLOAD_ERR_OK, // No error
			'size' => $fileSize,
		];

		$fileUpload = new FileUpload($fileData);

		if ($fileUpload->isOk()) {
			return $fileUpload;
		}

		throw new \RuntimeException( "Error creating FileUpload object: " . $fileUpload->getError());
	}
}
