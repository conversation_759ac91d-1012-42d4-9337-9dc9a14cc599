<?php declare(strict_types = 1);

namespace App\PostType\Core\Model;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;

interface ParentEntity
{

	public function getInternalName(): string;
	public function setInternalName(string $internalName): void;
	public function getCfSchemeJson(): string;
	public function getCfContent(): string;
	public function setCf(mixed $customFields): void;
	public function getLocalizations(): ICollection;
	public function getLocalization(Mutation $mutation): LocalizationEntity;

}
