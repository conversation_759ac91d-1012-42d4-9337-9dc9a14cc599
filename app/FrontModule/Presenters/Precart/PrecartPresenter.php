<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Precart;

use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDelivery;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Http\UrlScript;
use Nette\Utils\ArrayHash;


class PrecartPresenter extends BasePresenter
{
	private ?string $scId = null;

	private ?UrlScript $lastUrl = null;

	private ArrayHash $data;

	public function __construct(
		private readonly CartFreeDeliveryFactory $cartFreeDeliveryFactory,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		$this->scId = $this->getParameter('scId');
		$this->lastUrl = $this->session->getSection(self::class)->get('lastUrl');

		if ($this->scId === null) {
			if ($this->lastUrl !== null) {
				$this->redirectUrl((string) $this->lastUrl);
			}
			$this->redirect($this->mutation->pages->eshop);
		}

		if(($sessionData = $this->session->getSection(AddToCart::class)->get($this->scId)) === null) {
			if ($this->lastUrl !== null) {
				$this->redirectUrl((string) $this->lastUrl);
			}
			$this->redirect($this->mutation->pages->eshop);
		}

		$this->data = ArrayHash::from($sessionData);

		$this->session->getSection(self::class)->set('lastUrl', $sessionData['previousUrl'] ?? null);

		if (isset($sessionData['previousUrl'])) {
			$this->lastUrl = $this->template->lastUrl = $sessionData['previousUrl'];
		}
	}

	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(): void
	{
		$this->template->variant = $this->orm->productVariant->getById($this->data->variantId);
		$this->template->quantity = $this->data->quantity;
		$this->template->quantityAdded = $this->data->quantityAdded;
		$this->template->lastUrl = $this->lastUrl;
		$this->template->isAjax = $this->isAjax();

		if ($this->data->quantityAdded === 0) {
			$this->setView('error');
		}

		if($this->isAjax()){
			$this->getPayload()->modalSnippet = $this->getSnippetId('precart');
			$this->redrawControl('precart');
		}

	}

	public function afterRender(): void
	{
		if ($this->scId !== null){
			//$this->session->getSection(AddToCart::class)->remove($this->scId);
		}
	}

	public function createComponentFreeDelivery(): CartFreeDelivery
	{
		return $this->cartFreeDeliveryFactory->create($this->mutationHolder->getMutation(), $this->currentState, $this->priceLevel);
	}

}
