{block content}

{if $user->loggedIn}
	{snippetArea userOrderHistoryArea}
		{include 'default.latte'}
	{/snippetArea}
{elseif $object->uid == 'userOrderHistory'}
	<div class="row-main">
		{control breadcrumb}

		{foreach $flashes as $flash}
			<div class="message message-{$flash->type}">{_$flash->message}</div>
		{/foreach}

		{if $order}
			{snippet orderHistoryDetail}
				{control orderHistory:detail, $presenter->getParameter('orderHash')}
			{/snippet}
			{*include '../part/box/content.latte'*}
		{else}
			<div class="message message-error">{_'no_results_title'}</div>
		{/if}
	</div>
{/if}



