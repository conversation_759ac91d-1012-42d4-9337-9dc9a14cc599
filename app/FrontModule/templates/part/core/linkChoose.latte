{default $item = null}
{default $class = 'm-footer__link'}
{default $icon = false}
{default $isButton = false}
{default $hasArrow = false}
{php $type = $item->toggle}
{php $page = isset($item->systemHref) && isset($item->systemHref->page) ? $item->systemHref->page->getEntity() ?? false : false}
{php $hrefName = ($item->systemHref??->hrefName ?? false) ?: ($item->customHref??->hrefName ?? false)}
{php $href = $item->customHref??->href ?? false}

{if $type == 'systemHref' && $page}
	<a href="{plink $page}" n:ifcontent n:class="$class">
		<span n:tag-if="$isButton" class="btn__text">
			{if $icon}
				{php $img = $icon->getEntity()->getSize('icon')}
				<img src="{$img->src}" width="{$img->width}" height="{$img->height}" alt="" class="btn__icon">
			{/if}
			{if $hrefName}
				{$hrefName}
			{else}
				{$page->nameAnchor}
			{/if}
			<span n:if="$hasArrow" class="btn__arrow u-fw-n">&rarr;</span>
		</span>
	</a>
{elseif $type == 'customHref' && $href && $hrefName}
	{php $isAnchor = str_starts_with($href, '#')}
	<a href="{$href}" n:class="$class"{if !$isAnchor} target="_blank" rel="noopener noreferrer"{/if}>
		<span n:tag-if="$isButton" class="btn__text">
			{if $icon}
				{php $img = $icon->getEntity()->getSize('icon')}
				<img src="{$img->src}" width="{$img->width}" height="{$img->height}" alt="" class="btn__icon">
			{/if}
			{$hrefName}
			<span n:if="$hasArrow" class="btn__arrow u-fw-n">&rarr;</span>
		</span>
	</a>
{/if}
