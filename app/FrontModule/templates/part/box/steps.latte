{default $class = 'u-mb-sm'}
{default $currentStep = 1}

<div n:class="b-steps, $class">
	<ol class="b-steps__list grid grid--center">
		{define stepItem}
			{php $hasLink = $step < $currentStep || $currentStep + 1 == $step}
			<li n:class="b-steps__item, grid__cell, size--auto, $currentStep == $step ? is-active">
				<a n:tag="$link && $hasLink ? 'a' : 'span'"{if $link && $hasLink} href="{plink $link}"{/if} class="b-steps__inner" data-controller="autosubit" data-action="click->autosubmit#submitForm">
					{_$lang}
				</a>
			</li>
		{/define}

		{include stepItem, step: 1, lang: 'shopping_basket', link: $pages->cart}
		{include stepItem, step: 2, lang: 'shipping_payment', link: $pages->step1}
		{include stepItem, step: 3, lang: 'personal_info', link: $pages->step2}
		{* {include stepItem, step: 4, lang: 'order_success', link: $pages->step3} *}
	</ol>
</div>
