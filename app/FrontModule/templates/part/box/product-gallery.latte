{default $class = ''}

<div n:class="b-product-gallery, $class">
	<div class="b-product-gallery__main">
		<div class="b-product-gallery__img">
			{if $product->firstImage}
				{php $img = $product->firstImage->getSize('md')}
				{php $imgLg = $product->firstImage->getSize('xl')}
				<a href="{$imgLg->src}" class="b-product-gallery__link" data-modal='{"gallery": "product"}'>
					<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}">
				</a>
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" width="500" height="500">
			{/if}
		</div>
	</div>

	<div n:if="$product->images->count() > 1" class="b-product-gallery__thumbs u-pt-sm">
		<ul class="b-product-gallery__thumblist grid">
			{foreach $product->images as $i}
				{if !$iterator->first}
					{php $img = $i->getSize('sm')}
					{php $imgLg = $i->getSize('xl')}
					<li class="b-product-gallery__thumbitem grid__cell size--3-12{if $iterator->first} is-active{/if}">
						<a href="{$imgLg->src}" class="b-product-gallery__thumblink" data-modal='{"gallery": "product"}'>
							<img src="{$img->src}" alt="" loading="lazy" width="{$img->width}" height="{$img->height}">
						</a>
					</li>
				{/if}
			{/foreach}
		</ul>
	</div>
</div>
