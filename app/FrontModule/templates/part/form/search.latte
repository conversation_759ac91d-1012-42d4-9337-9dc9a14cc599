<form n:if="$pages->search !== null" action='{link $pages->search, suggest=>0, search=>null, filter=>null}' id="form-search" class="f-search u-pt-lg u-mg-lg u-pt-2xl@md u-mb-2xl@md">
	<p class="f-search__wrap u-mb-0">
		<span class="inp-fix inp-icon">
			<label for="search" class="inp-label u-vhide">{_search_label}</label>
			<input type="text" id="search" name="search" class="f-search__inp inp-text" value="" placeholder="{_search_placeholder}" autocomplete="off">
			{* <span class="inp-icon__icon">
				{('search')|icon}
				<span class="f-search__loader"></span>
			</span> *}
		</span>
		<button type="submit" class="f-search__btn btn btn--icon">
			<span class="btn__text">
				{('search')|icon, 'btn__icon'}
				<span class="u-vhide">{_btn_search}</span>
			</span>
		</button>
	</p>
</form>
