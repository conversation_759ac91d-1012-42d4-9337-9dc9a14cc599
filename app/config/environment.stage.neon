parameters:
	stageName: "stage"

	config:
		isDev: false
		env: stage

		elastica:
			host: sedlakova_es
			port: 9200

		cookie:
			enableModal: true

		mutations:
			cs:
				domain: sedlakovalegal-stage.www6.superkoderi.cz
				urlPrefix: false
				mutationId: 1
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				googleAnalyticsCode: '***********-1'
				robots: "index, follow"
				sitemap: true
				cookie:
					enableModal: true
			en:
				domain: sedlakovalegal-stage.www6.superkoderi.cz
				urlPrefix: en
				mutationId: 2
				rootId: 2
				hidePageId: 425
				systemPageId: 423
				internalName: english
				googleAnalyticsCode: '***********-1'
				robots: "index, follow"
				sitemap: true
				cookie:
					enableModal: true

		domainUrl: "https://superadmin-stage.www6.superkoderi.cz/"
		domainUrlPdf: "https://superadmin-stage.www6.superkoderi.cz/"

		translations:
			insertNew: true
			markUsage: true

messenger:
	transport:
		elasticFront:
			dsn: "sync://"
		elasticPriorityFront:
			dsn: "sync://"
		clonerFront:
			dsn: "sync://"

includes:
    - header.php

services:
	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")




