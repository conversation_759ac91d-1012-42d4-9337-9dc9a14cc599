	services:

		# Security
		authenticator: App\Model\Security\Authenticator
		acl: App\Model\Security\Acl
		user: App\Model\Security\User

#		odkomentovani: vypne cache
		cacheStorage:
			class: Nette\Caching\Storages\DevNullStorage

#		cacheStorage:
#			class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

		translatorDB:
			class: App\Model\TranslatorDB(%config.translations.insertNew%, %config.translations.markUsage%)


		translator:
			class: App\Model\Translator(%config.adminLang%, %config%)

		configService: App\Model\ConfigService(%config%)
		cacheFactory: App\Model\CacheFactory


		routerFactory: App\Router\RouterFactory
		router: @routerFactory::createRouter(%config.adminAlias%, %postTypeRoutes%)

		menuService:
			class: App\Model\MenuService

		- App\Model\ShoppingCart\ShoppingCart
		- App\Model\ShoppingCart\Storage\CookieStorage
		- App\Model\ShoppingCart\Handlers\DefaultUserAuthenticationHandler
		- App\Model\Orm\ProductVariant\Availability\ProductAvailabilityService
		- App\Model\Orm\ProductVariant\Availability\DefaultProductAvailability

		- App\Model\Orm\Order\NumberSequence\DefaultOrderNumberFormatter
		- App\Model\Orm\Order\NumberSequence\DefaultOrderNumberSequence
		- App\Model\Orm\Order\NumberSequence\OrderNumberGenerator

		# OrderPlaced subscribers
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\SendEmail
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\VoucherDeactivate
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\StockReservation

		# OrderStateChange subscribers
		- App\Model\Orm\Order\Event\OrderState\Subscriber\SendOrderStateEmail

		# DeliveryMethods
		- App\Model\Orm\DeliveryMethod\DeliveryMethodRegistry
		- App\Model\Orm\DeliveryMethod\PPL
		- App\Model\Orm\DeliveryMethod\Store

		# PaymentMethods
		- App\Model\Orm\PaymentMethod\PaymentMethodRegistry
		- App\Model\Orm\PaymentMethod\BankTransfer
		- App\Model\Orm\PaymentMethod\Card
		- App\Model\Orm\PaymentMethod\CashOnDelivery
		- App\Model\Orm\PaymentMethod\Store

		# CardPayment
		- App\Model\Orm\CardPayment\CardPaymentProcessor
		- App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry

		- App\Model\Orm\Order\OrderModel

		- App\AdminModule\Presenters\Library\LibraryPresenter

		- App\Model\FileLockFactory

		- \Curl\Curl

		- App\PostType\Page\Model\Orm\TreeModel
		- App\Model\Orm\File\FileModel
		- App\Model\Orm\Alias\AliasModel(adminAlias: %config.adminAlias%)
		- App\Model\Orm\AliasHistory\AliasHistoryModel
		- App\Model\Orm\User\UserModel
		- App\Model\Orm\UserHash\UserHashModel
		- App\Model\Orm\LibraryImage\LibraryImageModel
		- App\Model\Orm\LibraryTree\LibraryTreeModel
		- App\Model\Orm\Product\ProductModel
		- App\Model\Orm\ProductLocalization\ProductLocalizationModel
		- App\Model\Orm\ProductVariant\ProductVariantModel
		- App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationModel
		- App\PostType\Page\Model\Orm\CatalogTreeModel
		- App\Model\Orm\ProductReview\ProductReviewModel
		- App\Model\Orm\Parameter\ParameterModel
		- App\Model\Orm\ParameterValue\ParameterValueModel
		- App\Model\Orm\PriceLevel\PriceLevelModel
		- App\Model\Orm\VoucherCode\VoucherCodeModel

		- App\Model\PagesFactory

		- App\Model\Orm\EmailTemplate\EmailTemplateModel
		- App\Model\Email\EmailTemplateFactory
		- App\Model\DbalLog("%appDir%/../nettelog/", "mysql")
		- App\Model\EasyMessages
		- App\Model\Orm\State\StateModel
		- App\Model\Orm\Holiday\HolidayModel
		- App\Model\Orm\String\StringModel

		- App\Console\Model\Sitemap

		# Router
		- App\Model\Router\Filter
		- App\Model\Router\FilterLang
		- App\Model\Router\FilterSeoLink
		- App\Model\Router\FilterAlias
		- App\Model\Router\FilterCommonParameters
		- App\Model\Router\FilterFilterParams
		- App\Model\Router\FilterVariantId
		- App\Model\Router\FilterRedirect


		- App\Model\Link\LinkFactory
		- App\Model\Link\LinkSeo
		- App\Model\Link\LinkChecker
		- Nette\Http\UrlScript
		- App\Model\Orm\Redirect\RedirectModel
		- App\Model\ImageResizerWrapper
		- App\Model\Orm\Mutation\MutationModel
		- App\Model\Mutation\MutationsHolder
		- App\Model\Mutation\MutationHolder
		- App\Model\Mutation\MutationDetector
		- App\Model\Mutation\BrowserMutationDetector
		- App\Model\Url\UrlChecker
		- App\Model\Sentry\SentryLogger

		- App\Model\CustomField\LazyValueFactory

		- App\Model\Orm\NewsletterEmail\NewsletterEmailModel
		- App\Model\Orm\OrmCleaner

		- App\Model\Cloner\SiteCloner
		- App\Model\Cloner\MutationCloner
		- App\Model\Cloner\EsIndexCommonCloner
		- App\Model\Cloner\PageCommonCloner
		- App\Model\Cloner\ProductLocalizationCommonCloner
		- App\Model\Cloner\BlogTagLocalizationCommonCloner
		- App\Model\Cloner\AuthorLocalizationCommonCloner
		- App\Model\Cloner\SeoLinkLocalizationCommonCloner
		- App\Model\Cloner\BlogLocalizationCommonCloner
		- App\Model\Cloner\ClonerProvider

		- App\Model\Duplicator\ProductDuplicator
		- App\FrontModule\Components\AntispamSession
		- App\Model\Dbal\LogService


		# ROBOTS
		- App\Model\Robots\RobotsRows(%robotsTxt.rows%)

#		EMAILY
		- App\Model\Mailer\BaseMailer
		- App\Model\Email\CommonFactory

		nette.latteFactory:
			setup:
			- addFilter(timeAgoInWords, [App\Infrastructure\Latte\Filters, timeAgoInWords])
			- addFilter(plural, [App\Infrastructure\Latte\Filters, plural])
			- addFilter(parseVideoId, [App\Infrastructure\Latte\Filters, parseVideoId])
			- addFilter(niceDate, [App\Infrastructure\Latte\Filters, niceDate])
			- addFilter(money, [App\Infrastructure\Latte\Filters, formatMoney])
			- addFilter(texy, [App\Infrastructure\Latte\Filters, texy])
			- addFilter(skDate, [App\Infrastructure\Latte\Filters, skDate])
			- addFilter(prepareStrJs, [App\Infrastructure\Latte\Filters, prepareStrJs])
			- addFilter(clear, [App\Infrastructure\Latte\Filters, clear])
			- addFilter(copyright, [App\Infrastructure\Latte\Filters, copyright])
			- addFilter(icon, [App\Infrastructure\Latte\Filters, icon])
			- addFilter(lineExploder, [App\Infrastructure\Latte\Filters, lineExploder])
			- addFilter(exploder, [App\Infrastructure\Latte\Filters, exploder])
			- addFilter(stock, [App\Infrastructure\Latte\Filters, stock])
			- addFilter(phoneFormat, [App\Infrastructure\Latte\Filters, phoneFormat])
			- addFilter(formatNumberPrecision, [App\Infrastructure\Latte\Filters, formatNumberPrecision])
			- addFilter(tables, [App\Infrastructure\Latte\Filters, tables])
			- addFilter(lazyLoading, [App\Infrastructure\Latte\Filters, lazyLoading])
			- addFilter(obfuscateEmailAddresses, [App\Infrastructure\Latte\Filters, obfuscateEmailAddresses])
			- addFilter(formatSize, [App\Infrastructure\Latte\Filters, formatSize])

		- App\Model\Google\GoogleProviderFactory(
			clientId: %google.oauth.clientId%
			clientSecret: %google.oauth.clientSecret%
		)

		- App\Model\Form\FormThrottler

		- App\Model\MakeWebhookClient(%config.makeWebhook.url%)

