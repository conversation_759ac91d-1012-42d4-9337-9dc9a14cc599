<?php

declare(strict_types=1);

namespace App\Api\V1\Controllers\String\Update;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestBody;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Controllers\String\StringInputData;
use App\Api\V1\Controllers\String\StringItem;
use App\Model\Orm\Orm;
use App\Model\Orm\String\StringEntity;

#[Path('/')]
#[Tag('String')]
final class StringUpdateController extends BaseV1Controller
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	#[Path('/string/{name}')]
	#[Method('PUT')]
	#[RequestParameter(name: 'name', type: 'string', in: EndpointParameter::IN_PATH, required: true)]
	#[RequestBody(entity: StringInputData::class, required: true)]
	#[Response(description: 'Success', code: '200', entity: StringItem::class)]
	public function put(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$name = $request->getParameter('name');
		$strings = $this->orm->string->findBy(['name' => $name]);

		/** @var StringInputData $inputData */
		$inputData = $request->getEntity();

		foreach ($inputData->translations as $lg => $value) {
			$string = $strings->getBy(['lg' => $lg]);
			if ($string === null) {
				$string = new StringEntity();
				$string->name = $name;
				$string->lg = $lg;
			}

			$string->value = $value;
			$this->orm->persist($string);
		}

		$this->orm->flush();

		return $this->jsonResponse(
			(new StringItem($name, $inputData->translations))->toResponse(),
			$response,
		);
	}
}
