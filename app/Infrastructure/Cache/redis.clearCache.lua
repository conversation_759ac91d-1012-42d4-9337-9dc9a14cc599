local common_prefix = ARGV[1]
local current_prefix = ARGV[2]

local cursor = 0
local counter = 0
local tmp

repeat
  tmp = redis.call("SCAN", cursor, "MATCH", "*")
  cursor = tonumber(tmp[1])
  if tmp[2] then
    for k, v in pairs(tmp[2]) do
      if (v:find(common_prefix, 1, true) == 1 and v:find(current_prefix, 1, true) ~= 1) then
        redis.call("DEL", v)
        counter = counter + 1
      end
    end
  end
until cursor == 0
return counter
